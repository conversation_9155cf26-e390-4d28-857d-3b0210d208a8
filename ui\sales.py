from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import sys

class SalesWidget(QWidget):
    """واجهة إدارة المبيعات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.create_sample_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # العنوان الرئيسي
        title_label = QLabel("💰 إدارة المبيعات المتطورة - نظام شامل ومتقدم لإدارة المبيعات مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af,
                    stop:0.2 #3b82f6,
                    stop:0.4 #6366f1,
                    stop:0.6 #8b5cf6,
                    stop:0.8 #a855f7,
                    stop:1 #c084fc);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إطار البحث
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(10, 10, 10, 10)
        search_layout.setSpacing(10)

        # البحث
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626,
                    stop:0.5 #b91c1c,
                    stop:1 #991b1b);
                border: 3px solid #7f1d1d;
                border-radius: 12px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالعميل، رقم الفاتورة أو المنتج...")
        self.search_edit.textChanged.connect(self.filter_sales)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff,
                    stop:0.5 #f8fafc,
                    stop:1 #e2e8f0);
                border: 3px solid #4f46e5;
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
            }
        """)

        # فلتر الحالة
        status_label = QLabel("📊 حالة:")
        status_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b,
                    stop:0.5 #d97706,
                    stop:1 #b45309);
                border: 3px solid #92400e;
                border-radius: 12px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
            }
        """)
        status_label.setAlignment(Qt.AlignCenter)

        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "مدفوعة", "معلقة", "ملغية"])
        self.status_filter.currentIndexChanged.connect(self.filter_sales)
        self.status_filter.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff,
                    stop:0.5 #f8fafc,
                    stop:1 #e2e8f0);
                border: 3px solid #4f46e5;
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                min-width: 120px;
            }
        """)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit, 1)
        search_layout.addWidget(status_label)
        search_layout.addWidget(self.status_filter)
        search_layout.addStretch()

        search_frame.setLayout(search_layout)
        main_layout.addWidget(search_frame)

        # جدول المبيعات
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(9)
        self.sales_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "العميل", "التاريخ", "المنتج",
            "الكمية", "سعر الوحدة", "الإجمالي", "الحالة", "ملاحظات"
        ])

        # إعداد التحديد للسطر كاملاً
        self.sales_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sales_table.setSelectionMode(QAbstractItemView.SingleSelection)

        # تصميم الجدول
        self.sales_table.setStyleSheet("""
            QTableWidget {
                background-color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                gridline-color: #e5e7eb;
                font-size: 12px;
                selection-background-color: #3b82f6;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e5e7eb;
                font-weight: bold;
                color: #000000;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4f46e5,
                    stop:1 #3730a3);
                color: white;
                padding: 10px;
                border: 2px solid #1e1b4b;
                font-weight: bold;
                font-size: 13px;
            }
        """)

        # تعديل عرض الأعمدة
        header = self.sales_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # رقم الفاتورة
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # العميل
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # التاريخ
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # الكمية
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # سعر الوحدة
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # الإجمالي
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # الحالة
        header.setSectionResizeMode(8, QHeaderView.Stretch)  # ملاحظات

        # تحديد عرض الأعمدة
        self.sales_table.setColumnWidth(0, 100)  # رقم الفاتورة
        self.sales_table.setColumnWidth(2, 120)  # التاريخ
        self.sales_table.setColumnWidth(4, 80)   # الكمية
        self.sales_table.setColumnWidth(5, 100)  # سعر الوحدة
        self.sales_table.setColumnWidth(6, 100)  # الإجمالي
        self.sales_table.setColumnWidth(7, 80)   # الحالة

        main_layout.addWidget(self.sales_table, 1)

        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 5px;
                max-height: 70px;
                min-height: 65px;
            }
        """)

        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(10, 5, 10, 5)
        buttons_layout.setSpacing(10)

        # أزرار العمليات مثل المخزون مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة مبيعة")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_sale)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'primary')  # أزرق كلاسيكي
        self.edit_button.clicked.connect(self.edit_sale)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_sale)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة لعرض التفاصيل
        from ui.unified_styles import UnifiedStyles
        view_menu = QMenu(self)
        view_menu.setStyleSheet(UnifiedStyles.get_menu_style('indigo', 'normal'))

        view_details_action = QAction("👁️ عرض التفاصيل", self)
        view_details_action.triggered.connect(self.view_sale_details)
        view_menu.addAction(view_details_action)

        sales_history_action = QAction("📊 تاريخ المبيعات", self)
        sales_history_action.triggered.connect(self.view_sales_history)
        view_menu.addAction(sales_history_action)

        customer_info_action = QAction("👤 معلومات العميل", self)
        customer_info_action.triggered.connect(self.view_customer_info)
        view_menu.addAction(customer_info_action)

        self.view_button.setMenu(view_menu)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        excel_action = QAction("📊 Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        pdf_action = QAction("📄 PDF", self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        csv_action = QAction("📋 CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 JSON", self)
        json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(json_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة ملخص المبيعات محسن مثل المخزون
        self.total_sales_label = QLabel("إجمالي المبيعات: 0 | القيمة الإجمالية: 0.00 ج.م")
        self.total_sales_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #059669,
                    stop:0.5 #047857,
                    stop:1 #065f46);
                border: 3px solid #064e3b;
                border-radius: 12px;
                min-height: 34px;
                max-height: 38px;
            }
        """)
        self.total_sales_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.delete_button)
        buttons_layout.addWidget(self.refresh_button)
        buttons_layout.addWidget(self.view_button)
        buttons_layout.addWidget(self.export_button)
        buttons_layout.addWidget(self.statistics_button)
        buttons_layout.addWidget(self.total_sales_label)

        buttons_frame.setLayout(buttons_layout)
        main_layout.addWidget(buttons_frame)

        self.setLayout(main_layout)

    def filter_sales(self):
        """تصفية المبيعات"""
        search_text = self.search_edit.text().lower()
        status_filter = self.status_filter.currentText()
        
        for row in range(self.sales_table.rowCount()):
            show_row = True
            
            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(self.sales_table.columnCount()):
                    item = self.sales_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                
                if search_text not in row_text:
                    show_row = False
            
            # فلترة الحالة
            if status_filter != "جميع الحالات":
                status_item = self.sales_table.item(row, 7)
                if status_item and status_item.text() != status_filter:
                    show_row = False
            
            self.sales_table.setRowHidden(row, not show_row)
        
        # تحديث الملخص بعد التصفية
        self.update_summary()

    def create_sample_data(self):
        """إنشاء بيانات تجريبية للمبيعات"""
        sample_data = [
            ["S001", "أحمد محمد", "2024-01-15", "دهان أبيض", "20", "30.00", "600.00", "مدفوعة", "تم التسليم"],
            ["S002", "فاطمة علي", "2024-01-16", "سيراميك أرضي", "50", "20.00", "1000.00", "معلقة", "في الانتظار"],
            ["S003", "محمد حسن", "2024-01-17", "خشب صنوبر", "10", "120.00", "1200.00", "مدفوعة", "تم التسليم"],
            ["S004", "سارة أحمد", "2024-01-18", "مفاتيح كهربائية", "100", "8.00", "800.00", "مدفوعة", "تم التسليم"],
            ["S005", "عمر خالد", "2024-01-19", "حنفيات مياه", "15", "55.00", "825.00", "معلقة", "قيد المراجعة"]
        ]

        self.sales_table.setRowCount(len(sample_data))

        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                item.setFont(QFont("Arial", 10, QFont.Bold))
                item.setForeground(QColor("#000000"))

                # تلوين حسب الحالة
                if col == 7:  # عمود الحالة
                    if value == "مدفوعة":
                        item.setForeground(QColor("#059669"))
                    elif value == "معلقة":
                        item.setForeground(QColor("#d97706"))
                    elif value == "ملغية":
                        item.setForeground(QColor("#dc2626"))

                self.sales_table.setItem(row, col, item)

        # تحديث الملخص
        self.update_summary()

    def add_sale(self):
        """إضافة مبيعة جديدة"""
        QMessageBox.information(self, "قريباً", "ميزة إضافة مبيعة جديدة ستكون متاحة قريباً!")

    def edit_sale(self):
        """تعديل مبيعة"""
        current_row = self.sales_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مبيعة للتعديل")
            return

        # الحصول على بيانات السطر المحدد
        invoice_number = self.sales_table.item(current_row, 0).text() if self.sales_table.item(current_row, 0) else ""
        customer = self.sales_table.item(current_row, 1).text() if self.sales_table.item(current_row, 1) else ""
        date = self.sales_table.item(current_row, 2).text() if self.sales_table.item(current_row, 2) else ""
        product = self.sales_table.item(current_row, 3).text() if self.sales_table.item(current_row, 3) else ""
        quantity = self.sales_table.item(current_row, 4).text() if self.sales_table.item(current_row, 4) else ""
        unit_price = self.sales_table.item(current_row, 5).text() if self.sales_table.item(current_row, 5) else ""
        total = self.sales_table.item(current_row, 6).text() if self.sales_table.item(current_row, 6) else ""
        status = self.sales_table.item(current_row, 7).text() if self.sales_table.item(current_row, 7) else ""
        notes = self.sales_table.item(current_row, 8).text() if self.sales_table.item(current_row, 8) else ""

        # إنشاء نافذة التعديل
        dialog = QDialog(self)
        dialog.setWindowTitle(f"✏️ تعديل المبيعة: {invoice_number}")
        dialog.setModal(True)
        dialog.resize(500, 600)

        layout = QVBoxLayout()

        # حقول التعديل
        form_layout = QFormLayout()

        invoice_edit = QLineEdit(invoice_number)
        customer_edit = QLineEdit(customer)
        date_edit = QLineEdit(date)
        product_edit = QLineEdit(product)
        quantity_edit = QLineEdit(quantity)
        unit_price_edit = QLineEdit(unit_price)
        total_edit = QLineEdit(total)
        status_combo = QComboBox()
        status_combo.addItems(["مكتمل", "معلق", "ملغي"])
        status_combo.setCurrentText(status)
        notes_edit = QTextEdit(notes)
        notes_edit.setMaximumHeight(100)

        form_layout.addRow("رقم الفاتورة:", invoice_edit)
        form_layout.addRow("العميل:", customer_edit)
        form_layout.addRow("التاريخ:", date_edit)
        form_layout.addRow("المنتج:", product_edit)
        form_layout.addRow("الكمية:", quantity_edit)
        form_layout.addRow("سعر الوحدة:", unit_price_edit)
        form_layout.addRow("الإجمالي:", total_edit)
        form_layout.addRow("الحالة:", status_combo)
        form_layout.addRow("ملاحظات:", notes_edit)

        layout.addLayout(form_layout)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("💾 حفظ التعديلات")
        save_button.clicked.connect(lambda: self.save_sale_changes(
            current_row, invoice_edit.text(), customer_edit.text(), date_edit.text(),
            product_edit.text(), quantity_edit.text(), unit_price_edit.text(),
            total_edit.text(), status_combo.currentText(), notes_edit.toPlainText(), dialog
        ))
        buttons_layout.addWidget(save_button)

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(dialog.close)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def save_sale_changes(self, row, invoice, customer, date, product, quantity, unit_price, total, status, notes, dialog):
        """حفظ تعديلات المبيعة"""
        try:
            # تحديث البيانات في الجدول
            self.sales_table.setItem(row, 0, QTableWidgetItem(invoice))
            self.sales_table.setItem(row, 1, QTableWidgetItem(customer))
            self.sales_table.setItem(row, 2, QTableWidgetItem(date))
            self.sales_table.setItem(row, 3, QTableWidgetItem(product))
            self.sales_table.setItem(row, 4, QTableWidgetItem(quantity))
            self.sales_table.setItem(row, 5, QTableWidgetItem(unit_price))
            self.sales_table.setItem(row, 6, QTableWidgetItem(total))
            self.sales_table.setItem(row, 7, QTableWidgetItem(status))
            self.sales_table.setItem(row, 8, QTableWidgetItem(notes))

            # تطبيق التنسيق
            for col in range(9):
                item = self.sales_table.item(row, col)
                if item:
                    item.setFont(QFont("Arial", 10, QFont.Bold))
                    item.setForeground(QColor("#000000"))

                    # تلوين حسب الحالة
                    if col == 7:  # عمود الحالة
                        if status == "مكتمل":
                            item.setBackground(QColor("#d1fae5"))
                        elif status == "معلق":
                            item.setBackground(QColor("#fef3c7"))
                        elif status == "ملغي":
                            item.setBackground(QColor("#fee2e2"))

            QMessageBox.information(self, "تم", "تم حفظ التعديلات بنجاح!")
            dialog.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ التعديلات: {str(e)}")

    def delete_sale(self):
        """حذف مبيعة"""
        current_row = self.sales_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "تأكيد الحذف", "هل أنت متأكد من حذف هذه المبيعة؟")
            if reply == QMessageBox.Yes:
                self.sales_table.removeRow(current_row)
                self.update_summary()
                QMessageBox.information(self, "نجح", "تم حذف المبيعة بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مبيعة للحذف")

    def refresh_data(self):
        """تحديث البيانات"""
        self.create_sample_data()
        QMessageBox.information(self, "تم التحديث", "تم تحديث بيانات المبيعات بنجاح!")

    def update_summary(self):
        """تحديث ملخص المبيعات"""
        try:
            total_count = 0
            total_value = 0.0
            
            for row in range(self.sales_table.rowCount()):
                if not self.sales_table.isRowHidden(row):
                    total_count += 1
                    total_item = self.sales_table.item(row, 6)  # عمود الإجمالي
                    if total_item:
                        total_value += float(total_item.text())
            
            self.total_sales_label.setText(f"إجمالي المبيعات: {total_count} | القيمة الإجمالية: {total_value:.2f} ج.م")
            
        except Exception as e:
            print(f"خطأ في تحديث الملخص: {str(e)}")

    def show_statistics(self):
        """عرض إحصائيات المبيعات"""
        try:
            from database import Sale, get_session
            session = get_session()
            sales = session.query(Sale).all()

            if not sales:
                QMessageBox.information(self, "إحصائيات المبيعات", "لا توجد مبيعات لعرض الإحصائيات")
                return

            # حساب الإحصائيات
            total_sales = len(sales)
            total_amount = sum(sale.total_amount or 0 for sale in sales)
            avg_amount = total_amount / total_sales if total_sales > 0 else 0

            # إحصائيات حسب العميل
            customer_stats = {}
            for sale in sales:
                customer_name = sale.customer.name if sale.customer else 'غير محدد'
                if customer_name in customer_stats:
                    customer_stats[customer_name]['count'] += 1
                    customer_stats[customer_name]['amount'] += sale.total_amount or 0
                else:
                    customer_stats[customer_name] = {
                        'count': 1,
                        'amount': sale.total_amount or 0
                    }

            # إحصائيات حسب الحالة
            status_stats = {}
            for sale in sales:
                status = sale.status or 'غير محدد'
                if status in status_stats:
                    status_stats[status] += 1
                else:
                    status_stats[status] = 1

            # إنشاء نافذة الإحصائيات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 إحصائيات المبيعات")
            dialog.setModal(True)
            dialog.resize(700, 600)

            layout = QVBoxLayout()

            # الإحصائيات العامة
            general_stats = f"""
📊 الإحصائيات العامة:
─────────────────────────────────────────────────────────────────────────────
💰 إجمالي المبيعات: {total_sales}
💵 إجمالي المبلغ: {int(total_amount):,} جنيه
📈 متوسط قيمة المبيعة: {int(avg_amount):,} جنيه

👥 توزيع حسب العميل:
─────────────────────────────────────────────────────────────────────────────
"""

            # أفضل 5 عملاء
            sorted_customers = sorted(customer_stats.items(), key=lambda x: x[1]['amount'], reverse=True)[:5]
            for customer, stats in sorted_customers:
                percentage = (stats['count'] / total_sales) * 100
                general_stats += f"• {customer}: {stats['count']} مبيعة ({percentage:.1f}%) - {int(stats['amount']):,} جنيه\n"

            general_stats += f"""
─────────────────────────────────────────────────────────────────────────────

📋 توزيع حسب الحالة:
─────────────────────────────────────────────────────────────────────────────
"""

            for status, count in status_stats.items():
                percentage = (count / total_sales) * 100
                general_stats += f"• {status}: {count} مبيعة ({percentage:.1f}%)\n"

            # عرض الإحصائيات
            stats_text = QTextBrowser()
            stats_text.setPlainText(general_stats)
            stats_text.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 11px;
                    line-height: 1.4;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(stats_text)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            export_stats_btn = QPushButton("📤 تصدير الإحصائيات")
            export_stats_btn.clicked.connect(lambda: self.export_statistics_report(general_stats))
            buttons_layout.addWidget(export_stats_btn)

            close_button = QPushButton("❌ إغلاق")
            close_button.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_button)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #60a5fa, stop:0.5 #3b82f6, stop:1 #2563eb)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1d4ed8, stop:1 #1e40af)',
                    'border': '#1e40af'
                },
                'emerald': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #10b981, stop:0.5 #059669, stop:1 #047857)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #34d399, stop:0.5 #10b981, stop:1 #059669)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #047857, stop:1 #065f46)',
                    'border': '#065f46'
                },
                'danger': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f87171, stop:0.5 #ef4444, stop:1 #dc2626)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #b91c1c, stop:1 #991b1b)',
                    'border': '#991b1b'
                },
                'modern_teal': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #14b8a6, stop:0.5 #0d9488, stop:1 #0f766e)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5eead4, stop:0.5 #14b8a6, stop:1 #0d9488)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0f766e, stop:1 #134e4a)',
                    'border': '#134e4a'
                },
                'indigo': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #6366f1, stop:0.5 #4f46e5, stop:1 #4338ca)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #a5b4fc, stop:0.5 #6366f1, stop:1 #4f46e5)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4338ca, stop:1 #3730a3)',
                    'border': '#3730a3'
                },
                'info': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #06b6d4, stop:0.5 #0891b2, stop:1 #0e7490)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #67e8f9, stop:0.5 #06b6d4, stop:1 #0891b2)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0e7490, stop:1 #155e75)',
                    'border': '#155e75'
                },
                'rose': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f43f5e, stop:0.5 #e11d48, stop:1 #be123c)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fb7185, stop:0.5 #f43f5e, stop:1 #e11d48)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #be123c, stop:1 #9f1239)',
                    'border': '#9f1239'
                }
            }

            # الحصول على ألوان الزر
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور
            style = f"""
                QPushButton {{
                    background: {color_scheme['normal']};
                    color: #ffffff;
                    border: 3px solid {color_scheme['border']};
                    border-radius: 12px;
                    padding: 10px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    min-height: 35px;
                    max-height: 45px;
                }}
                QPushButton:hover {{
                    background: {color_scheme['hover']};
                    border: 3px solid {color_scheme['border']};
                    transform: translateY(-2px);
                }}
                QPushButton:pressed {{
                    background: {color_scheme['pressed']};
                    border: 3px solid {color_scheme['border']};
                    transform: translateY(1px);
                }}
                QPushButton:disabled {{
                    background: #9ca3af;
                    color: #6b7280;
                    border: 3px solid #6b7280;
                }}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر: {str(e)}")
            # تطبيق تصميم بسيط في حالة الخطأ
            button.setStyleSheet("""
                QPushButton {
                    background-color: #3b82f6;
                    color: white;
                    border: 2px solid #1e40af;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2563eb;
                }
            """)

    def export_to_excel(self):
        """تصدير المبيعات إلى Excel (CSV)"""
        self.export_to_csv()

    def export_to_csv(self):
        """تصدير المبيعات إلى CSV"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ ملف CSV", "المبيعات.csv", "CSV Files (*.csv)")
            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    headers = ['الرقم', 'رقم المبيعة', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'الحالة']
                    writer.writerow(headers)

                    for row in range(self.sales_table.rowCount()):
                        row_data = []
                        for col in range(self.sales_table.columnCount()):
                            item = self.sales_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)
                QMessageBox.information(self, "تم", f"تم تصدير المبيعات بنجاح إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير المبيعات إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المبيعات", "تقرير_المبيعات.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير المبيعات</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #6366f1; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>💰 تقرير المبيعات</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>رقم المبيعة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                        </tr>
                """

                total_amount = 0
                for row in range(self.sales_table.rowCount()):
                    sale_id = self.sales_table.item(row, 0).text() if self.sales_table.item(row, 0) else ""
                    sale_number = self.sales_table.item(row, 1).text() if self.sales_table.item(row, 1) else ""
                    customer = self.sales_table.item(row, 2).text() if self.sales_table.item(row, 2) else ""
                    date = self.sales_table.item(row, 3).text() if self.sales_table.item(row, 3) else ""
                    amount_text = self.sales_table.item(row, 4).text() if self.sales_table.item(row, 4) else "0"
                    status = self.sales_table.item(row, 5).text() if self.sales_table.item(row, 5) else ""

                    try:
                        amount = float(amount_text.replace(',', '').replace('جنيه', '').strip())
                        total_amount += amount
                    except:
                        amount = 0

                    html_content += f"""
                        <tr>
                            <td>{sale_id}</td>
                            <td>{sale_number}</td>
                            <td>{customer}</td>
                            <td>{date}</td>
                            <td>{int(amount):,} جنيه</td>
                            <td>{status}</td>
                        </tr>
                    """

                html_content += f"""
                    </table>
                    <h3>إجمالي المبيعات: {int(total_amount):,} جنيه</h3>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                QMessageBox.information(self, "تم", f"تم تصدير المبيعات إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير المبيعات إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "المبيعات.json", "JSON Files (*.json)"
            )

            if file_path:
                data = []
                for row in range(self.sales_table.rowCount()):
                    row_data = {
                        'الرقم': self.sales_table.item(row, 0).text() if self.sales_table.item(row, 0) else "",
                        'رقم المبيعة': self.sales_table.item(row, 1).text() if self.sales_table.item(row, 1) else "",
                        'العميل': self.sales_table.item(row, 2).text() if self.sales_table.item(row, 2) else "",
                        'التاريخ': self.sales_table.item(row, 3).text() if self.sales_table.item(row, 3) else "",
                        'المبلغ الإجمالي': self.sales_table.item(row, 4).text() if self.sales_table.item(row, 4) else "",
                        'الحالة': self.sales_table.item(row, 5).text() if self.sales_table.item(row, 5) else ""
                    }
                    data.append(row_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "تم", f"تم تصدير المبيعات إلى JSON بنجاح:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير JSON: {str(e)}")

    def export_statistics_report(self, stats_content):
        """تصدير تقرير الإحصائيات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإحصائيات", "إحصائيات_المبيعات.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            📊 تقرير إحصائيات المبيعات
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

{stats_content}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المبيعات
═══════════════════════════════════════════════════════════════════════════════
""")

                QMessageBox.information(self, "تم", f"تم تصدير تقرير الإحصائيات بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")

    def view_sale_details(self):
        """عرض تفاصيل المبيعة المحددة"""
        selected_row = self.sales_table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مبيعة من القائمة")
            return

        # الحصول على جميع بيانات السطر المحدد
        invoice_number = self.sales_table.item(selected_row, 0).text() if self.sales_table.item(selected_row, 0) else "غير محدد"
        customer = self.sales_table.item(selected_row, 1).text() if self.sales_table.item(selected_row, 1) else "غير محدد"
        date = self.sales_table.item(selected_row, 2).text() if self.sales_table.item(selected_row, 2) else "غير محدد"
        product = self.sales_table.item(selected_row, 3).text() if self.sales_table.item(selected_row, 3) else "غير محدد"
        quantity = self.sales_table.item(selected_row, 4).text() if self.sales_table.item(selected_row, 4) else "0"
        unit_price = self.sales_table.item(selected_row, 5).text() if self.sales_table.item(selected_row, 5) else "0"
        total = self.sales_table.item(selected_row, 6).text() if self.sales_table.item(selected_row, 6) else "0"
        status = self.sales_table.item(selected_row, 7).text() if self.sales_table.item(selected_row, 7) else "غير محدد"
        notes = self.sales_table.item(selected_row, 8).text() if self.sales_table.item(selected_row, 8) else "لا توجد ملاحظات"

        # إنشاء نافذة التفاصيل
        dialog = QDialog(self)
        dialog.setWindowTitle(f"👁️ تفاصيل المبيعة: {invoice_number}")
        dialog.setModal(True)
        dialog.resize(600, 500)

        layout = QVBoxLayout()

        # معلومات المبيعة
        details_text = f"""
💰 تفاصيل المبيعة:
─────────────────────────────────────────────────────────────────────────────
📋 رقم الفاتورة: {invoice_number}
👤 العميل: {customer}
📅 التاريخ: {date}
📦 المنتج: {product}
📊 الكمية: {quantity}
💰 سعر الوحدة: {unit_price}
💵 الإجمالي: {total}
📋 الحالة: {status}
📝 ملاحظات: {notes}
─────────────────────────────────────────────────────────────────────────────

💡 معلومات إضافية:
• تم إنشاء هذا السجل في النظام
• يمكن تعديل البيانات من خلال زر التعديل
• يمكن تصدير هذه البيانات مع التقارير
        """

        # عرض التفاصيل
        details_browser = QTextBrowser()
        details_browser.setPlainText(details_text)
        details_browser.setStyleSheet("""
            QTextBrowser {
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11px;
                line-height: 1.4;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        layout.addWidget(details_browser)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        edit_button = QPushButton("✏️ تعديل")
        edit_button.clicked.connect(lambda: (dialog.close(), self.edit_sale()))
        buttons_layout.addWidget(edit_button)

        print_button = QPushButton("🖨️ طباعة")
        print_button.clicked.connect(lambda: self.print_sale_details(invoice_number, customer, date, product, quantity, unit_price, total, status, notes))
        buttons_layout.addWidget(print_button)

        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(dialog.close)
        buttons_layout.addWidget(close_button)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def print_sale_details(self, invoice, customer, date, product, quantity, unit_price, total, status, notes):
        """طباعة تفاصيل المبيعة"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تفاصيل المبيعة", f"تفاصيل_المبيعة_{invoice}.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            💰 تفاصيل المبيعة
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ الطباعة: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الطباعة: {QDate.currentDate().toString('hh:mm:ss')}

📋 معلومات المبيعة:
─────────────────────────────────────────────────────────────────────────────
رقم الفاتورة: {invoice}
العميل: {customer}
التاريخ: {date}
المنتج: {product}
الكمية: {quantity}
سعر الوحدة: {unit_price}
الإجمالي: {total}
الحالة: {status}
ملاحظات: {notes}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المبيعات
═══════════════════════════════════════════════════════════════════════════════
""")

                QMessageBox.information(self, "تم", f"تم حفظ تفاصيل المبيعة بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ التفاصيل: {str(e)}")

    def view_sales_history(self):
        """عرض تاريخ المبيعات"""
        QMessageBox.information(self, "تاريخ المبيعات", "ميزة تاريخ المبيعات ستكون متاحة قريباً!")

    def view_customer_info(self):
        """عرض معلومات العميل"""
        selected_row = self.sales_table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مبيعة من القائمة")
            return

        customer = self.sales_table.item(selected_row, 2).text() if self.sales_table.item(selected_row, 2) else ""
        QMessageBox.information(self, "معلومات العميل", f"معلومات العميل: {customer}")
